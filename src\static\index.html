<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Study Assistant - CBSE Class 9</title>
    <meta name="description" content="AI-powered study assistant for CBSE Class 9 students">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Enhanced Formatting Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/chat.css">
    <link rel="stylesheet" href="/static/css/enhanced-formatting.css">
    <!-- Add D3.js for interactive diagrams -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Add Mermaid for flowcharts -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js" defer></script>
    <!-- Theme Manager -->
    <script src="/static/js/theme-manager.js" defer></script>
    <style>
        /* Prevent flash of unstyled content */
        body { opacity: 0; transition: opacity 0.3s ease; }
        body.loaded { opacity: 1; }

        /* Fallback to show content after 2 seconds if scripts don't load */
        body {
            animation: showContent 0.3s ease 2s forwards;
        }

        @keyframes showContent {
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Transform Your Learning with AI-Powered Study Assistant</h1>
                <p class="hero-subtitle">Experience revolutionary textbook comprehension with our advanced RAG agent that provides instant answers, personalized study plans, and comprehensive learning support for students and parents.</p>
                
                <div class="hero-buttons">
                    <button class="btn-primary" id="get-started-btn">
                        <span>Start Chatting with AI</span>
                        <i class="fas fa-comments"></i>
                    </button>
                    <button class="btn-secondary" id="watch-demo-btn">
                        <i class="fas fa-play"></i>
                        <span>Watch Demo</span>
                    </button>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="floating-card card-1">
                    <div class="card-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="card-content">
                        <h4>98.4% Accuracy</h4>
                        <p>History Questions</p>
                    </div>
                </div>
                
                <div class="floating-card card-2">
                    <div class="card-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="card-content">
                        <h4>99.0% Coverage</h4>
                        <p>Political Science</p>
                    </div>
                </div>
                
                <div class="floating-card card-3">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="card-content">
                        <h4>Sub-second</h4>
                        <p>Response Time</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Agentic RAG Chat Section -->
    <section class="chat-demo-section" id="agentic-chat">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Ask Your Questions - Get Instant Answers</h2>
                <p class="section-subtitle">Experience our Agentic RAG AI that understands your textbooks and provides accurate, contextual answers</p>
            </div>

            <div class="chat-demo-container">
                <div class="chat-demo-card glass-card">
                    <div class="chat-demo-header">
                        <div class="chat-demo-title">
                            <i class="fas fa-robot"></i>
                            <span>AI Study Assistant</span>
                        </div>
                        <div class="chat-demo-status">
                            <div class="status-dot"></div>
                            <span>Online</span>
                        </div>
                    </div>

                    <div class="chat-demo-messages">
                        <div class="demo-message ai-message">
                            <div class="message-avatar">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="message-content">
                                <p>Hi! I'm your AI study assistant. I can help you with questions about History, Political Science, Geography, and Economics from your Class 9 textbooks.</p>
                            </div>
                        </div>

                        <div class="demo-message user-message">
                            <div class="message-content">
                                <p>What were the main causes of the French Revolution?</p>
                            </div>
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>

                        <div class="demo-message ai-message">
                            <div class="message-avatar">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="message-content">
                                <p>The French Revolution had several key causes: Economic crisis due to debt and taxation, Social inequality between estates, Political absolutism of the monarchy, and Enlightenment ideas promoting liberty and equality.</p>
                                <div class="message-sources">
                                    <small><i class="fas fa-book"></i> Source: History Class 9 Textbook, Chapter 1</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chat-demo-input">
                        <button class="btn-primary chat-demo-btn" id="start-chat-btn">
                            <i class="fas fa-comments"></i>
                            <span>Start Asking Questions</span>
                        </button>
                        <p class="chat-demo-note">Click to open the interactive chat and ask your own questions!</p>
                    </div>
                </div>

                <div class="chat-features">
                    <div class="feature-highlight">
                        <div class="feature-icon">
                            <i class="fas fa-lightning-bolt"></i>
                        </div>
                        <h4>Instant Responses</h4>
                        <p>Get answers in under 1 second</p>
                    </div>

                    <div class="feature-highlight">
                        <div class="feature-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h4>Textbook Accurate</h4>
                        <p>Answers based on your curriculum</p>
                    </div>

                    <div class="feature-highlight">
                        <div class="feature-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h4>Learning Focused</h4>
                        <p>Explanations that help you understand</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Student Benefits Section -->
    <section class="benefits-section" id="student-benefits">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Benefits for Students</h2>
                <p class="section-subtitle">Empower your learning journey with AI-driven educational tools</p>
            </div>
            
            <div class="benefits-grid">
                <div class="glass-card benefit-card">
                    <div class="card-icon-large">
                        <i class="fas fa-lightbulb gradient-icon"></i>
                    </div>
                    <h3 class="card-title">Get Answers Instantly</h3>
                    <p class="card-description">Access immediate responses to your questions with 98.4% accuracy in History and 99.0% coverage in Political Science. Never struggle with homework again.</p>
                    <div class="chat-preview">
                        <div class="chat-bubble user">What caused World War I?</div>
                        <div class="chat-bubble ai">The assassination of Archduke Franz Ferdinand in 1914 was the immediate trigger, but underlying causes included...</div>
                    </div>
                </div>
                
                <div class="glass-card benefit-card">
                    <div class="card-icon-large">
                        <i class="fas fa-file-alt gradient-icon"></i>
                    </div>
                    <h3 class="card-title">AI-Generated Study Notes</h3>
                    <p class="card-description">Get comprehensive, exam-ready summaries and chapter breakdowns tailored to your learning style and curriculum requirements.</p>
                    <div class="notes-preview">
                        <div class="note-item">
                            <div class="note-dot"></div>
                            <span>Key concepts highlighted</span>
                        </div>
                        <div class="note-item">
                            <div class="note-dot"></div>
                            <span>Practice questions included</span>
                        </div>
                        <div class="note-item">
                            <div class="note-dot"></div>
                            <span>Visual mind maps</span>
                        </div>
                    </div>
                </div>
                
                <div class="glass-card benefit-card">
                    <div class="card-icon-large">
                        <i class="fas fa-brain gradient-icon"></i>
                    </div>
                    <h3 class="card-title">Higher-Order Thinking Skills</h3>
                    <p class="card-description">Develop critical thinking and analytical skills through complex question handling and guided problem-solving approaches.</p>
                    <div class="thinking-map">
                        <div class="thinking-node center">Critical Analysis</div>
                        <div class="thinking-node node-1">Compare</div>
                        <div class="thinking-node node-2">Evaluate</div>
                        <div class="thinking-node node-3">Synthesize</div>
                        <div class="connection con-1"></div>
                        <div class="connection con-2"></div>
                        <div class="connection con-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Parent Benefits Section -->
    <section class="parent-benefits-section" id="parent-benefits">
        <div class="container">
            <div class="parent-benefits-layout">
                <div class="parent-content">
                    <h2 class="section-title dark">Benefits for Parents</h2>
                    <p class="section-subtitle dark">Stay connected with your child's learning progress and provide better support</p>
                    
                    <div class="parent-features">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Real-time Progress Tracking</h4>
                                <p>Monitor your child's learning journey with detailed analytics, engagement metrics, and performance insights.</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Safe Learning Environment</h4>
                                <p>FERPA compliant platform with robust data security measures ensuring your child's privacy and safety.</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Cost-Effective Education</h4>
                                <p>Save up to 80% compared to traditional tutoring while providing 24/7 personalized learning assistance.</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="feature-content">
                                <h4>24/7 Availability</h4>
                                <p>Round-the-clock learning support that adapts to your family's schedule and your child's learning pace.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="parent-dashboard">
                    <div class="glass-card dashboard-card">
                        <h3>Student Dashboard</h3>
                        <div class="dashboard-stats">
                            <div class="stat-item">
                                <div class="stat-value">87%</div>
                                <div class="stat-label">Weekly Progress</div>
                                <div class="stat-bar">
                                    <div class="stat-fill" style="width: 87%"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">24</div>
                                <div class="stat-label">Questions Asked</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12% this week</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.2h</div>
                                <div class="stat-label">Study Time</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>Above average</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Features Section -->
    <section class="tech-features-section" id="tech-features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title light">Advanced AI Technology</h2>
                <p class="section-subtitle light">Powered by cutting-edge RAG pipeline architecture</p>
            </div>
            
            <div class="tech-grid">
                <div class="glass-card tech-card">
                    <div class="tech-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>Vector Database</h3>
                    <div class="tech-stat">3,111</div>
                    <p>Indexed Chunks</p>
                    <div class="tech-detail">Pinecone Integration</div>
                </div>
                
                <div class="glass-card tech-card">
                    <div class="tech-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>AI Models</h3>
                    <div class="tech-models">
                        <div class="model-badge">GPT-3.5</div>
                        <div class="model-badge">multilingual-e5-large</div>
                    </div>
                    <div class="tech-detail">Advanced Language Processing</div>
                </div>
                
                <div class="glass-card tech-card">
                    <div class="tech-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3>Response Time</h3>
                    <div class="tech-stat">&lt;1s</div>
                    <p>Average Retrieval</p>
                    <div class="performance-bar">
                        <div class="performance-fill"></div>
                    </div>
                </div>
                
                <div class="glass-card tech-card coverage-card">
                    <div class="tech-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3>Content Coverage</h3>
                    <div class="coverage-stats">
                        <div class="coverage-item">
                            <div class="coverage-label">History</div>
                            <div class="coverage-bar">
                                <div class="coverage-fill" style="width: 98.4%"></div>
                            </div>
                            <div class="coverage-percent">98.4%</div>
                        </div>
                        <div class="coverage-item">
                            <div class="coverage-label">Political Science</div>
                            <div class="coverage-bar">
                                <div class="coverage-fill" style="width: 99%"></div>
                            </div>
                            <div class="coverage-percent">99.0%</div>
                        </div>
                        <div class="coverage-item">
                            <div class="coverage-label">Geography</div>
                            <div class="coverage-bar">
                                <div class="coverage-fill" style="width: 95.7%"></div>
                            </div>
                            <div class="coverage-percent">95.7%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">Get in Touch</h2>
                <p class="cta-subtitle">Ready to transform your learning experience? Start your journey today</p>
                
                <div class="cta-buttons">
                    <button class="btn-primary cta-btn">
                        <i class="fas fa-calendar"></i>
                        <span>Book a Counselling</span>
                    </button>
                    <button class="btn-accent cta-btn">
                        <i class="fas fa-download"></i>
                        <span>Download the Visa Mentor App</span>
                    </button>
                    <button class="btn-success cta-btn">
                        <i class="fab fa-whatsapp"></i>
                        <span>Connect on WhatsApp</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Floating Chat Button -->
    <div id="floating-chat-btn" class="floating-chat-btn">
        <div class="chat-btn-content">
            <i class="fas fa-comments"></i>
            <span class="chat-btn-text">Start Learning</span>
        </div>
        <div class="chat-btn-notification">
            Click to start your learning journey!
        </div>
    </div>

    <!-- Chat Interface -->
    <div id="chat-interface" class="hidden">
        <div class="chat-header">
            <div class="chat-title">
                <h3>AI Study Assistant</h3>
                <div class="chat-status">
                    <span class="status-dot online"></span>
                    <span>Online</span>
                </div>
            </div>
            <div class="chat-controls">
                <button class="control-btn" id="theme-toggle-btn" aria-label="Toggle theme" title="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>

                <button class="control-btn" id="close-chat" aria-label="Close chat" title="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="chat-messages" id="chat-messages">
            <!-- Messages will be added here dynamically -->
        </div>

        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <textarea id="query-input" 
                         placeholder="Type your question here and unlock your brilliance✨!" 
                         rows="1"
                         aria-label="Message input"
                         maxlength="2000"></textarea>
                <div class="input-buttons">
                    <button class="input-btn" id="attach-btn" aria-label="Attach file" title="Attach file">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <button class="input-btn" id="voice-btn" aria-label="Voice message" title="Voice message">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="input-btn" id="emoji-btn" aria-label="Add emoji" title="Add emoji">
                        <i class="fas fa-smile"></i>
                    </button>
                    <button class="send-btn" id="send-button" disabled aria-label="Send message" title="Send">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Emoji Picker -->
    <div id="emoji-picker" class="emoji-picker hidden">
        <div class="emoji-categories">
            <button class="category-btn active" data-category="smileys">😊</button>
            <button class="category-btn" data-category="people">👋</button>
            <button class="category-btn" data-category="nature">🌺</button>
            <button class="category-btn" data-category="food">🍎</button>
            <button class="category-btn" data-category="activities">⚽</button>
            <button class="category-btn" data-category="travel">🚗</button>
            <button class="category-btn" data-category="objects">💡</button>
            <button class="category-btn" data-category="symbols">❤️</button>
        </div>
        <div id="emoji-grid" class="emoji-grid"></div>
    </div>

    <!-- File Input (hidden) -->
    <input type="file" id="file-input" class="hidden" accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png">

    <!-- SVG Definitions -->
    <svg width="0" height="0" style="position: absolute;">
        <defs>
            <marker id="arrow" viewBox="0 0 10 10" refX="9" refY="5"
                    markerWidth="6" markerHeight="6" orient="auto">
                <path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b"/>
            </marker>
        </defs>
    </svg>

    <!-- Enhanced Formatting Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/contrib/auto-render.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js" defer></script>

    <!-- Enhanced Application Scripts -->
    <script src="/static/js/enhanced-formatter.js" defer></script>
    <script src="/static/js/visual-content-generator.js" defer></script>
    <script src="/static/js/concept-diagrams.js" defer></script>
    <script src="/static/js/diagram-interactions.js" defer></script>
    <script src="/static/app.js" defer></script>


</body>
</html>